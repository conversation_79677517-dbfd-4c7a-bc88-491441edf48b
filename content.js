// content.js
let cosmeticSelectors = [];
let observer = null;
let styleElement = null;

function applyCosmeticFiltering(selectors) {
  if (!document.head) {
    // Wait for head to be available
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () =>
        applyCosmeticFiltering(selectors)
      );
    } else {
      // Head should be there, but if not, maybe this page is weird
      setTimeout(() => applyCosmeticFiltering(selectors), 100);
    }
    return;
  }

  // Remove old style element if it exists
  if (styleElement && styleElement.parentNode) {
    styleElement.parentNode.removeChild(styleElement);
  }
  styleElement = null;

  if (!selectors || selectors.length === 0) {
    console.log(
      "No cosmetic selectors to apply or filtering disabled for this page."
    );
    if (observer) {
      observer.disconnect(); // Stop observing if no rules
      observer = null;
    }
    return;
  }

  const cssRules =
    selectors.join(",\n") +
    ` { 
            display: none !important; 
            visibility: hidden !important; 
            opacity: 0 !important; 
            width: 0 !important; 
            height: 0 !important; 
            margin: 0 !important; 
            padding: 0 !important; 
            font-size: 0 !important; 
            line-height: 0 !important;
            overflow: hidden !important;
            pointer-events: none !important;
         }`;

  styleElement = document.createElement("style");
  styleElement.type = "text/css";
  styleElement.textContent = cssRules;
  (document.head || document.documentElement).appendChild(styleElement);
  // console.log("Cosmetic filters applied with selectors:", selectors.length);

  // Initial hide for elements already present
  try {
    document.querySelectorAll(selectors.join(",")).forEach((el) => {
      // Styles already applied by stylesheet, but this can be a fallback
      // el.style.setProperty('display', 'none', 'important');
    });
  } catch (e) {
    // console.warn("Error during initial querySelectorAll for cosmetic filtering:", e);
  }

  // Observe DOM changes for dynamically added ads
  if (!observer) {
    observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if the node itself matches
              for (const selector of selectors) {
                try {
                  if (node.matches(selector)) {
                    // Style is applied by stylesheet, this is just for logging
                    // node.style.setProperty('display', 'none', 'important');
                    // console.log('Dynamically added ad (node itself) hidden:', node);
                    break;
                  }
                } catch (e) {
                  /* Invalid selector for matches(), ignore */
                }
              }
              // Check if any children of the new node match
              // This is important because ads can be complex structures
              try {
                if (node.querySelector(selectors.join(","))) {
                  node
                    .querySelectorAll(selectors.join(","))
                    .forEach((adElement) => {
                      // adElement.style.setProperty('display', 'none', 'important');
                      // console.log('Dynamically added ad (child) hidden:', adElement);
                    });
                }
              } catch (e) {
                /* Invalid selector for querySelectorAll(), ignore */
              }
            }
          });
        }
      }
    });
  }

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true,
  });
}

async function initContentScript() {
  try {
    const response = await chrome.runtime.sendMessage({
      action: "getBlockingStatus",
    });
    if (response && response.enabled) {
      const selectorsResponse = await chrome.runtime.sendMessage({
        action: "getCosmeticSelectors",
      });
      if (selectorsResponse && selectorsResponse.selectors) {
        cosmeticSelectors = selectorsResponse.selectors;
        applyCosmeticFiltering(cosmeticSelectors);
      }
    } else {
      // Blocking is disabled or page is whitelisted
      applyCosmeticFiltering([]); // This will remove styles and stop observer
    }
  } catch (e) {
    // console.warn("Content script: Error communicating with background or applying filters.", e);
    // This can happen if the extension was just updated/reloaded
    // Or if the page is privileged (e.g. chrome:// pages)
  }
}

// Listen for messages from background (e.g., if settings change)
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "UPDATE_COSMETIC_FILTERS") {
    if (request.enabled && request.selectors) {
      cosmeticSelectors = request.selectors;
      applyCosmeticFiltering(cosmeticSelectors);
    } else {
      applyCosmeticFiltering([]); // Disable/clear filters
    }
    sendResponse({ status: "Cosmetic filters updated in content script" });
  }
  return true;
});

// Initial run
// `run_at: document_start` means this runs very early.
// DOMContentLoaded might not have fired yet.
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initContentScript);
} else {
  initContentScript();
}
