<!DOCTYPE html>
<html>
  <head>
    <title>Ad Blocker Options</title>
    <meta charset="UTF-8" />
    <style>
      * {
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        padding: 20px;
        background-color: #f8f9fa;
        color: #333;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto;
      }

      h1 {
        color: #007bff;
        text-align: center;
        margin-bottom: 30px;
        font-weight: 300;
      }

      .section {
        background: white;
        padding: 25px;
        margin-bottom: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .section h2 {
        margin-top: 0;
        color: #495057;
        font-size: 1.25rem;
        font-weight: 500;
      }

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #495057;
      }

      textarea {
        width: 100%;
        min-height: 150px;
        margin-bottom: 15px;
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 14px;
        resize: vertical;
        transition: border-color 0.3s ease;
      }

      textarea:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
      }

      button {
        padding: 12px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        min-width: 120px;
      }

      button:hover {
        background-color: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      button:active {
        transform: translateY(0);
      }

      .notification {
        padding: 12px 16px;
        margin-bottom: 20px;
        border-radius: 6px;
        font-weight: 500;
        animation: slideIn 0.3s ease;
      }

      .notification-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .notification-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .notification-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .help-text {
        font-size: 13px;
        color: #6c757d;
        margin-top: 8px;
        line-height: 1.4;
      }

      .example {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 12px;
        margin-top: 10px;
        border-left: 3px solid #007bff;
      }
    </style>
  </head>
  <body>
    <h1>Ad Blocker Options</h1>

    <div class="section">
      <h2>Manage Whitelisted Sites</h2>
      <label for="whitelistTextarea">Whitelisted Domains (one per line):</label>
      <textarea
        id="whitelistTextarea"
        placeholder="example.com&#10;google.com&#10;youtube.com"
      ></textarea>
      <div class="help-text">
        Add domains where you want to disable ad blocking. Enter one domain per
        line without "http://" or "https://". Subdomains will be automatically
        included (e.g., adding "example.com" will also whitelist
        "www.example.com").
      </div>
      <div class="example">
        Examples:<br />
        google.com<br />
        youtube.com<br />
        reddit.com
      </div>
      <button id="saveWhitelistButton">Save Whitelist</button>
    </div>

    <div class="section">
      <h2>Manage Custom Cosmetic Filters</h2>
      <label for="cosmeticFiltersTextarea">CSS Selectors (one per line):</label>
      <textarea
        id="cosmeticFiltersTextarea"
        placeholder=".custom-ad-banner&#10;#annoying-popup&#10;div[data-ad='true']"
      ></textarea>
      <div class="help-text">
        Add custom CSS selectors to hide specific ad elements that aren't caught
        by the default filters. These selectors will be combined with the
        built-in ad blocking rules. Use browser developer tools to find the
        right selectors.
      </div>
      <div class="example">
        Examples:<br />
        .custom-ad-class<br />
        #sidebar-ads<br />
        div[data-advertisement]<br />
        .sponsored-content
      </div>
      <button id="saveCosmeticFiltersButton">Save Cosmetic Filters</button>
    </div>

    <script src="options.js"></script>
  </body>
</html>
