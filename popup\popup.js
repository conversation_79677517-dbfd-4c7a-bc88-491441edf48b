// popup.js
document.addEventListener("DOMContentLoaded", async () => {
  const globalEnableSwitch = document.getElementById("globalEnableSwitch");
  const globalEnableText = document.getElementById("globalEnableText");
  const blockingStatusText = document.getElementById("blockingStatusText");
  const siteSpecificSection = document.getElementById("siteSpecificSection");
  const currentDomainText = document.getElementById("currentDomain");
  const whitelistButton = document.getElementById("whitelistButton");

  let currentTabDomain = null;

  // Get current status from background
  try {
    const response = await chrome.runtime.sendMessage({
      action: "getBlockingStatus",
    });
    updatePopupUI(response);

    if (
      response.domain &&
      (response.domain.startsWith("http") || !response.domain.includes("."))
    ) {
      // Simple check if it's a real domain
      currentTabDomain = response.domain;
      currentDomainText.textContent = currentTabDomain;
      siteSpecificSection.style.display = "block";
      if (response.whitelisted) {
        whitelistButton.textContent = `Re-enable blocking for ${currentTabDomain}`;
        whitelistButton.classList.add("remove-whitelist");
      } else {
        whitelistButton.textContent = `Disable blocking for ${currentTabDomain}`;
        whitelistButton.classList.remove("remove-whitelist");
      }
      whitelistButton.disabled = !response.globalEnabled; // Can only whitelist if global blocking is on
    } else {
      siteSpecificSection.style.display = "none";
    }
  } catch (e) {
    console.error("Error getting blocking status:", e);
    blockingStatusText.textContent = "Error";
    blockingStatusText.style.color = "red";
  }

  globalEnableSwitch.addEventListener("change", async () => {
    const enable = globalEnableSwitch.checked;
    try {
      const response = await chrome.runtime.sendMessage({
        action: "toggleBlocking",
        enable: enable,
      });
      // Request updated status to refresh UI completely
      const newStatus = await chrome.runtime.sendMessage({
        action: "getBlockingStatus",
      });
      updatePopupUI(newStatus);
      if (currentTabDomain) {
        // Re-evaluate whitelist button state
        whitelistButton.disabled = !newStatus.globalEnabled;
        if (newStatus.whitelisted) {
          whitelistButton.textContent = `Re-enable blocking for ${currentTabDomain}`;
          whitelistButton.classList.add("remove-whitelist");
        } else {
          whitelistButton.textContent = `Disable blocking for ${currentTabDomain}`;
          whitelistButton.classList.remove("remove-whitelist");
        }
      }
    } catch (e) {
      console.error("Error toggling blocking:", e);
    }
  });

  whitelistButton.addEventListener("click", async () => {
    if (!currentTabDomain) return;

    try {
      const currentStatus = await chrome.runtime.sendMessage({
        action: "getBlockingStatus",
      });
      if (currentStatus.whitelisted) {
        await chrome.runtime.sendMessage({
          action: "removeFromWhitelist",
          domain: currentTabDomain,
        });
      } else {
        await chrome.runtime.sendMessage({
          action: "addToWhitelist",
          domain: currentTabDomain,
        });
      }
      // Refresh UI
      const newStatus = await chrome.runtime.sendMessage({
        action: "getBlockingStatus",
      });
      updatePopupUI(newStatus);
      if (newStatus.whitelisted) {
        whitelistButton.textContent = `Re-enable blocking for ${currentTabDomain}`;
        whitelistButton.classList.add("remove-whitelist");
      } else {
        whitelistButton.textContent = `Disable blocking for ${currentTabDomain}`;
        whitelistButton.classList.remove("remove-whitelist");
      }
      // Force content script to re-evaluate cosmetic filters on the current tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0] && tabs[0].id) {
          chrome.runtime
            .sendMessage({ action: "getCosmeticSelectors" })
            .then((response) => {
              if (response && response.selectors) {
                chrome.tabs.sendMessage(tabs[0].id, {
                  action: "UPDATE_COSMETIC_FILTERS",
                  enabled: newStatus.enabled, // actual current blocking status for the page
                  selectors: response.selectors,
                });
              }
            });
        }
      });
    } catch (e) {
      console.error("Error updating whitelist:", e);
    }
  });

  function updatePopupUI(status) {
    if (!status) {
      blockingStatusText.textContent = "Error loading status";
      blockingStatusText.style.color = "orange";
      return;
    }
    globalEnableSwitch.checked = status.globalEnabled;

    if (status.globalEnabled) {
      if (status.whitelisted) {
        blockingStatusText.textContent = "Disabled for this site (Whitelisted)";
        blockingStatusText.style.color = "orange";
      } else {
        blockingStatusText.textContent = "ACTIVE";
        blockingStatusText.style.color = "#28a745"; // Green
      }
    } else {
      blockingStatusText.textContent = "Globally DISABLED";
      blockingStatusText.style.color = "red";
    }
  }
});
