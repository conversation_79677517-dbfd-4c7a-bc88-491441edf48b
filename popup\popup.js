// popup.js
document.addEventListener("DOMContentLoaded", async () => {
  const globalEnableSwitch = document.getElementById("globalEnableSwitch");
  const blockingStatusText = document.getElementById("blockingStatusText");
  const statusDot = document.getElementById("statusDot");
  const siteSpecificSection = document.getElementById("siteSpecificSection");
  const currentDomainText = document.getElementById("currentDomain");
  const whitelistButton = document.getElementById("whitelistButton");
  const blockedCount = document.getElementById("blockedCount");
  const totalBlocked = document.getElementById("totalBlocked");
  const optionsLink = document.getElementById("optionsLink");
  const helpLink = document.getElementById("helpLink");

  let currentTabDomain = null;

  // Load and display statistics
  await loadStatistics();

  // Get current status from background
  try {
    const response = await chrome.runtime.sendMessage({
      action: "getBlockingStatus",
    });
    updatePopupUI(response);

    if (
      response.domain &&
      (response.domain.startsWith("http") || !response.domain.includes("."))
    ) {
      // Simple check if it's a real domain
      currentTabDomain = response.domain;
      currentDomainText.textContent = currentTabDomain;
      siteSpecificSection.style.display = "block";
      if (response.whitelisted) {
        whitelistButton.textContent = `Re-enable blocking`;
        whitelistButton.className = "action-button danger";
      } else {
        whitelistButton.textContent = `Disable blocking`;
        whitelistButton.className = "action-button primary";
      }
      whitelistButton.disabled = !response.globalEnabled; // Can only whitelist if global blocking is on
    } else {
      siteSpecificSection.style.display = "none";
    }
  } catch (e) {
    console.error("Error getting blocking status:", e);
    blockingStatusText.textContent = "Error";
    blockingStatusText.style.color = "red";
  }

  globalEnableSwitch.addEventListener("change", async () => {
    const enable = globalEnableSwitch.checked;
    try {
      const response = await chrome.runtime.sendMessage({
        action: "toggleBlocking",
        enable: enable,
      });
      // Request updated status to refresh UI completely
      const newStatus = await chrome.runtime.sendMessage({
        action: "getBlockingStatus",
      });
      updatePopupUI(newStatus);
      if (currentTabDomain) {
        // Re-evaluate whitelist button state
        whitelistButton.disabled = !newStatus.globalEnabled;
        if (newStatus.whitelisted) {
          whitelistButton.textContent = `Re-enable blocking`;
          whitelistButton.className = "action-button danger";
        } else {
          whitelistButton.textContent = `Disable blocking`;
          whitelistButton.className = "action-button primary";
        }
      }
    } catch (e) {
      console.error("Error toggling blocking:", e);
    }
  });

  whitelistButton.addEventListener("click", async () => {
    if (!currentTabDomain) return;

    try {
      const currentStatus = await chrome.runtime.sendMessage({
        action: "getBlockingStatus",
      });
      if (currentStatus.whitelisted) {
        await chrome.runtime.sendMessage({
          action: "removeFromWhitelist",
          domain: currentTabDomain,
        });
      } else {
        await chrome.runtime.sendMessage({
          action: "addToWhitelist",
          domain: currentTabDomain,
        });
      }
      // Refresh UI
      const newStatus = await chrome.runtime.sendMessage({
        action: "getBlockingStatus",
      });
      updatePopupUI(newStatus);
      if (newStatus.whitelisted) {
        whitelistButton.textContent = `Re-enable blocking`;
        whitelistButton.className = "action-button danger";
      } else {
        whitelistButton.textContent = `Disable blocking`;
        whitelistButton.className = "action-button primary";
      }
      // Force content script to re-evaluate cosmetic filters on the current tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0] && tabs[0].id) {
          chrome.runtime
            .sendMessage({ action: "getCosmeticSelectors" })
            .then((response) => {
              if (response && response.selectors) {
                chrome.tabs.sendMessage(tabs[0].id, {
                  action: "UPDATE_COSMETIC_FILTERS",
                  enabled: newStatus.enabled, // actual current blocking status for the page
                  selectors: response.selectors,
                });
              }
            });
        }
      });
    } catch (e) {
      console.error("Error updating whitelist:", e);
    }
  });

  function updatePopupUI(status) {
    if (!status) {
      blockingStatusText.textContent = "Error loading status";
      statusDot.className = "status-dot warning";
      return;
    }
    globalEnableSwitch.checked = status.globalEnabled;

    if (status.globalEnabled) {
      if (status.whitelisted) {
        blockingStatusText.textContent = "Disabled for this site";
        statusDot.className = "status-dot warning";
      } else {
        blockingStatusText.textContent = "Protection Active";
        statusDot.className = "status-dot";
      }
    } else {
      blockingStatusText.textContent = "Protection Disabled";
      statusDot.className = "status-dot disabled";
    }
  }

  // Statistics loading function
  async function loadStatistics() {
    try {
      const stats = await chrome.storage.local.get([
        "blockedToday",
        "totalBlocked",
        "lastResetDate",
      ]);

      // Reset daily count if it's a new day
      const today = new Date().toDateString();
      if (stats.lastResetDate !== today) {
        await chrome.storage.local.set({
          blockedToday: 0,
          lastResetDate: today,
        });
        blockedCount.textContent = "0";
      } else {
        blockedCount.textContent = (stats.blockedToday || 0).toLocaleString();
      }

      totalBlocked.textContent = (stats.totalBlocked || 0).toLocaleString();
    } catch (error) {
      console.error("Error loading statistics:", error);
      blockedCount.textContent = "0";
      totalBlocked.textContent = "0";
    }
  }

  // Event listeners for footer links
  optionsLink.addEventListener("click", (e) => {
    e.preventDefault();
    chrome.runtime.openOptionsPage();
    window.close();
  });

  helpLink.addEventListener("click", (e) => {
    e.preventDefault();
    chrome.tabs.create({
      url: "https://github.com/your-repo/ad-remover-extension#help",
    });
    window.close();
  });
});
