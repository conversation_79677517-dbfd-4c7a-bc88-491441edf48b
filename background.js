// background.js

const DYNAMIC_RULE_ID_START = 1000; // Start IDs for dynamic rules high to avoid conflict with static
let nextDynamicRuleId = DYNAMIC_RULE_ID_START;

// --- Storage Helpers ---
async function getStorage(key) {
  return new Promise((resolve) => {
    chrome.storage.local.get(key, (result) => resolve(result[key]));
  });
}

async function setStorage(data) {
  return new Promise((resolve) => {
    chrome.storage.local.set(data, resolve);
  });
}

// --- Whitelist Management ---
async function getWhitelist() {
  return (await getStorage("whitelist")) || [];
}

async function addToWhitelist(domain) {
  const whitelist = await getWhitelist();
  if (!whitelist.includes(domain)) {
    whitelist.push(domain);
    await setStorage({ whitelist });
    await updateDynamicRulesForWhitelist(); // Re-evaluate rules
  }
}

async function removeFromWhitelist(domain) {
  let whitelist = await getWhitelist();
  whitelist = whitelist.filter((d) => d !== domain);
  await setStorage({ whitelist });
  await updateDynamicRulesForWhitelist(); // Re-evaluate rules
}

async function isWhitelisted(url) {
  if (!url) return false;
  try {
    const domain = new URL(url).hostname;
    const whitelist = await getWhitelist();
    return whitelist.some(
      (whitelistedDomain) =>
        domain === whitelistedDomain || domain.endsWith("." + whitelistedDomain)
    );
  } catch (e) {
    console.warn("Invalid URL for whitelist check:", url, e);
    return false;
  }
}

// --- Dynamic Network Rules (e.g., for whitelisting or complex filters) ---
async function updateDynamicRulesForWhitelist() {
  const whitelist = await getWhitelist();
  const dynamicRules = await chrome.declarativeNetRequest.getDynamicRules();
  const removeRuleIds = dynamicRules.map((rule) => rule.id);

  const addRules = [];
  if (whitelist.length > 0) {
    addRules.push({
      id: DYNAMIC_RULE_ID_START, // Use a consistent ID for the whitelist rule
      priority: 100, // Higher priority to override blocking rules
      action: { type: "allow" },
      condition: {
        initiatorDomains: whitelist, // For MV3, requestDomains is preferred if applicable
        resourceTypes: [
          "main_frame",
          "sub_frame",
          "script",
          "image",
          "xmlhttprequest",
          "media",
          "object",
          "stylesheet",
          "font",
          "websocket",
          "other",
        ],
      },
    });
    // If initiatorDomains is not enough, you might need to use requestDomains or urlFilter
    // Example with requestDomains:
    // "requestDomains": whitelist,
  }

  await chrome.declarativeNetRequest.updateDynamicRules({
    removeRuleIds: removeRuleIds,
    addRules: addRules,
  });
  console.log("Dynamic rules updated for whitelist:", whitelist);
}

// --- Cosmetic Filtering Management ---
// In a real extension, these would come from parsed filter lists
const DEFAULT_COSMETIC_SELECTORS = [
  'div[id*="ad"]',
  'div[class*="ad-"]',
  'div[class*="-ad"]',
  'div[id*="banner"]',
  'div[class*="banner"]',
  'div[id*="sponsor"]',
  'div[class*="sponsor"]',
  'div[aria-label*="advertisement"]',
  'div[aria-label*="Advertisement"]',
  "ins.adsbygoogle",
  ".ad-container",
  ".advertisement",
  ".ad-wrapper",
  ".ad-unit",
  "#bottomads",
  "#topads",
  ".bottom-ads",
  ".top-ads",
  'iframe[name*="google_ads_iframe"]',
  "[data-ad-format]",
  "[data-ad-layout-key]",
  "[data-ad-slot]",
];

async function getCosmeticSelectors() {
  return (await getStorage("cosmeticSelectors")) || DEFAULT_COSMETIC_SELECTORS;
}

async function updateCosmeticFiltersForTab(tabId, selectors) {
  try {
    // Remove previous dynamic CSS first if any were injected this way
    // (More robust methods exist, e.g., tracking injected CSS IDs)
    await chrome.scripting.removeCSS({
      target: { tabId: tabId, allFrames: true },
      files: [], // This is tricky, usually you'd give an ID or remove specific CSS text
    });

    if (selectors && selectors.length > 0) {
      const cssContent =
        selectors.join(", ") +
        " { display: none !important; opacity: 0 !important; height: 0 !important; width: 0 !important; margin:0 !important; padding:0 !important; overflow:hidden !important; }";
      await chrome.scripting.insertCSS({
        target: { tabId: tabId, allFrames: true },
        css: cssContent,
      });
    }
  } catch (e) {
    if (
      !e.message.includes("No CSS detected") &&
      !e.message.includes("Cannot access contents of url")
    ) {
      console.warn(`Error injecting/removing CSS for tab ${tabId}:`, e);
    }
  }
}

// --- Event Listeners ---
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log("Extension installed or updated.", details);
  await setStorage({ blockingEnabled: true }); // Enable by default
  await updateDynamicRulesForWhitelist(); // Initialize whitelist rules (likely empty)
  // In a real extension, fetch/update filter lists here
  // For example: scheduleNextFilterUpdate();
});

// Update cosmetic filters when a tab is updated or created
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === "loading" || changeInfo.status === "complete") {
    if (
      tab.url &&
      (tab.url.startsWith("http:") || tab.url.startsWith("https:"))
    ) {
      const isSiteWhitelisted = await isWhitelisted(tab.url);
      const globalBlockingEnabled = await getStorage("blockingEnabled");

      if (globalBlockingEnabled && !isSiteWhitelisted) {
        const selectors = await getCosmeticSelectors();
        updateCosmeticFiltersForTab(tabId, selectors);
      } else {
        updateCosmeticFiltersForTab(tabId, []); // Remove filters if disabled or whitelisted
      }
    }
  }
});

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  (async () => {
    if (request.action === "getBlockingStatus") {
      const globalEnabled = await getStorage("blockingEnabled");
      const whitelisted =
        sender.tab && sender.tab.url
          ? await isWhitelisted(sender.tab.url)
          : false;
      const domain =
        sender.tab && sender.tab.url ? new URL(sender.tab.url).hostname : null;
      sendResponse({
        enabled: globalEnabled && !whitelisted,
        globalEnabled,
        whitelisted,
        domain,
      });
    } else if (request.action === "toggleBlocking") {
      await setStorage({ blockingEnabled: request.enable });
      // Update all tabs (simplified)
      const tabs = await chrome.tabs.query({
        url: ["http://*/*", "https://*/*"],
      });
      for (const tab of tabs) {
        const isSiteWhitelisted = await isWhitelisted(tab.url);
        if (request.enable && !isSiteWhitelisted) {
          const selectors = await getCosmeticSelectors();
          updateCosmeticFiltersForTab(tab.id, selectors);
        } else {
          updateCosmeticFiltersForTab(tab.id, []);
        }
      }
      sendResponse({ newStatus: request.enable });
    } else if (request.action === "addToWhitelist") {
      await addToWhitelist(request.domain);
      sendResponse({ success: true, domain: request.domain });
    } else if (request.action === "removeFromWhitelist") {
      await removeFromWhitelist(request.domain);
      sendResponse({ success: true, domain: request.domain });
    } else if (request.action === "getCosmeticSelectors") {
      const selectors = await getCosmeticSelectors();
      sendResponse({ selectors });
    }
  })();
  return true; // Indicates an asynchronous response
});

// Initialize whitelist rules on startup
updateDynamicRulesForWhitelist();

console.log("Enhanced Ad Blocker Background Script Loaded.");
