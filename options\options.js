// options.js
document.addEventListener("DOMContentLoaded", async () => {
  const whitelistTextarea = document.getElementById("whitelistTextarea");
  const saveWhitelistButton = document.getElementById("saveWhitelistButton");
  const cosmeticFiltersTextarea = document.getElementById(
    "cosmeticFiltersTextarea"
  );
  const saveCosmeticFiltersButton = document.getElementById(
    "saveCosmeticFiltersButton"
  );

  // Load whitelist
  const storedWhitelist = await chrome.storage.local.get("whitelist");
  if (storedWhitelist.whitelist) {
    whitelistTextarea.value = storedWhitelist.whitelist.join("\n");
  }

  // Load custom cosmetic filters
  const storedCosmeticFilters = await chrome.storage.local.get(
    "customCosmeticSelectors"
  );
  if (storedCosmeticFilters.customCosmeticSelectors) {
    cosmeticFiltersTextarea.value =
      storedCosmeticFilters.customCosmeticSelectors.join("\n");
  }

  saveWhitelistButton.addEventListener("click", async () => {
    const domains = whitelistTextarea.value
      .split("\n")
      .map((d) => d.trim())
      .filter((d) => d.length > 0);
    await chrome.storage.local.set({ whitelist: domains });
    // Inform background script to update rules (important!)
    chrome.runtime.sendMessage(
      { action: "refreshRulesFromStorage" },
      (response) => {
        if (chrome.runtime.lastError) {
          console.warn(
            "Error sending refreshRulesFromStorage message:",
            chrome.runtime.lastError.message
          );
        } else {
          alert("Whitelist saved! Rules will be updated.");
        }
      }
    );
    // You'd also need to handle `refreshRulesFromStorage` in background.js
    // to call `updateDynamicRulesForWhitelist()`
  });

  saveCosmeticFiltersButton.addEventListener("click", async () => {
    const selectors = cosmeticFiltersTextarea.value
      .split("\n")
      .map((s) => s.trim())
      .filter((s) => s.length > 0);

    // In a real app, you'd merge these with default selectors or manage them separately
    // For this demo, let's assume these replace/become the main user-defined list
    // The background script would then combine these with any built-in defaults.
    await chrome.storage.local.set({ customCosmeticSelectors: selectors });

    // Inform background script to update cosmetic filters on active tabs
    chrome.runtime.sendMessage(
      { action: "refreshCosmeticFiltersFromStorage" },
      (response) => {
        if (chrome.runtime.lastError) {
          console.warn(
            "Error sending refreshCosmeticFiltersFromStorage message:",
            chrome.runtime.lastError.message
          );
        } else {
          alert("Cosmetic filters saved! Active tabs will be updated.");
        }
      }
    );
    // You'd need to handle `refreshCosmeticFiltersFromStorage` in background.js
    // It should re-fetch selectors (DEFAULT_COSMETIC_SELECTORS + customCosmeticSelectors)
    // and then call `updateCosmeticFiltersForTab` for all relevant tabs.
  });
});

// Add to background.js the handlers for these messages:
/*
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // ... existing listeners ...
  if (request.action === "refreshRulesFromStorage") {
    (async () => {
      await updateDynamicRulesForWhitelist(); // This re-reads from storage
      sendResponse({status: "Whitelist rules refreshed"});
    })();
    return true;
  } else if (request.action === "refreshCosmeticFiltersFromStorage") {
    (async () => {
      // Re-fetch all selectors (default + custom from storage)
      const defaultSels = DEFAULT_COSMETIC_SELECTORS; // Assuming it's accessible
      const customSels = (await getStorage('customCosmeticSelectors')) || [];
      const allSelectors = [...new Set([...defaultSels, ...customSels])]; // Combine and remove duplicates
      
      await setStorage({ cosmeticSelectors: allSelectors }); // Update the combined list in storage

      // Update all active tabs
      const tabs = await chrome.tabs.query({ url: ["http://* /*", "https://* /*"] });
      for (const tab of tabs) {
        const isSiteWhitelisted = await isWhitelisted(tab.url);
        const globalBlockingEnabled = await getStorage('blockingEnabled');
        if (globalBlockingEnabled && !isSiteWhitelisted) {
          updateCosmeticFiltersForTab(tab.id, allSelectors);
        } else {
          updateCosmeticFiltersForTab(tab.id, []);
        }
      }
      sendResponse({status: "Cosmetic filters refreshed for active tabs"});
    })();
    return true;
  }
});
*/
