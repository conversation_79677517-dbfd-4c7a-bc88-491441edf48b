<!DOCTYPE html>
<html>
  <head>
    <title>Ad Blocker Popup</title>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="popup.css" />
    <style>
      body {
        font-family: sans-serif;
        width: 250px;
        padding: 10px;
        background-color: #f4f4f4;
        color: #333;
      }

      .container {
        text-align: center;
      }

      h1 {
        font-size: 1.2em;
        color: #007bff;
        margin-bottom: 15px;
      }

      .status-section p,
      .toggle-section span {
        font-size: 0.9em;
      }

      .status-section strong {
        color: #28a745; /* Green for enabled, change in JS for disabled */
      }

      .toggle-section {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 50px; /* Adjusted width */
        height: 28px; /* Adjusted height */
        margin-right: 10px;
      }

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 20px; /* Adjusted size */
        width: 20px; /* Adjusted size */
        left: 4px; /* Adjusted position */
        bottom: 4px; /* Adjusted position */
        background-color: white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
      }

      input:checked + .slider {
        background-color: #28a745;
      }

      input:focus + .slider {
        box-shadow: 0 0 1px #28a745;
      }

      input:checked + .slider:before {
        -webkit-transform: translateX(22px); /* Adjusted translation */
        -ms-transform: translateX(22px);
        transform: translateX(22px);
      }

      .slider.round {
        border-radius: 28px; /* Adjusted for height */
      }

      .slider.round:before {
        border-radius: 50%;
      }

      #siteSpecificSection button {
        padding: 8px 12px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9em;
        transition: background-color 0.3s ease;
      }

      #siteSpecificSection button:hover {
        background-color: #0056b3;
      }

      #siteSpecificSection button.remove-whitelist {
        background-color: #dc3545;
      }
      #siteSpecificSection button.remove-whitelist:hover {
        background-color: #c82333;
      }

      .footer-note {
        font-size: 0.7em;
        color: #666;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Enhanced Ad Blocker</h1>

      <div class="status-section">
        <p>Status: <strong id="blockingStatusText">Loading...</strong></p>
      </div>

      <div class="toggle-section">
        <label class="switch">
          <input type="checkbox" id="globalEnableSwitch" />
          <span class="slider round"></span>
        </label>
        <span id="globalEnableText">Global Blocking</span>
      </div>

      <div id="siteSpecificSection" style="display: none">
        <hr />
        <p>For <strong id="currentDomain">this site</strong>:</p>
        <button id="whitelistButton">Whitelist Site</button>
      </div>

      <p class="footer-note">This is a demo extension.</p>
    </div>
    <script src="popup.js"></script>
  </body>
</html>
