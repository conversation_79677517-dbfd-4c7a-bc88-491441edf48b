<!DOCTYPE html>
<html>
  <head>
    <title>Ad Blocker Popup</title>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="popup.css" />
    <style>
      * {
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        width: 320px;
        padding: 0;
        margin: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        overflow: hidden;
      }

      .container {
        background: white;
        padding: 20px;
        border-radius: 12px 12px 0 0;
        box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
      }

      .header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
      }

      h1 {
        font-size: 1.4em;
        color: #495057;
        margin: 0;
        font-weight: 600;
      }

      .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-top: 8px;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #28a745;
        animation: pulse 2s infinite;
      }

      .status-dot.disabled {
        background-color: #dc3545;
        animation: none;
      }

      .status-dot.warning {
        background-color: #ffc107;
        animation: none;
      }

      @keyframes pulse {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
        100% {
          opacity: 1;
        }
      }

      .status-text {
        font-size: 0.9em;
        font-weight: 500;
      }

      .toggle-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 15px;
      }

      .toggle-label {
        font-weight: 500;
        color: #495057;
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 54px;
        height: 30px;
      }

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.3s;
        border-radius: 30px;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.3s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      input:checked + .slider {
        background-color: #28a745;
      }

      input:checked + .slider:before {
        transform: translateX(24px);
      }

      .site-section {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 15px;
      }

      .site-section h3 {
        margin: 0 0 10px 0;
        font-size: 0.9em;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .current-site {
        font-weight: 600;
        color: #495057;
        margin-bottom: 12px;
        word-break: break-all;
      }

      .action-button {
        width: 100%;
        padding: 10px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9em;
        font-weight: 500;
        transition: all 0.3s ease;
        text-transform: none;
      }

      .action-button.primary {
        background-color: #007bff;
        color: white;
      }

      .action-button.primary:hover {
        background-color: #0056b3;
        transform: translateY(-1px);
      }

      .action-button.danger {
        background-color: #dc3545;
        color: white;
      }

      .action-button.danger:hover {
        background-color: #c82333;
        transform: translateY(-1px);
      }

      .action-button:disabled {
        background-color: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
        transform: none;
      }

      .footer {
        text-align: center;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .footer-links {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 8px;
      }

      .footer-link {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        font-size: 0.8em;
        transition: color 0.3s ease;
      }

      .footer-link:hover {
        color: white;
      }

      .version {
        font-size: 0.7em;
        opacity: 0.7;
      }

      .stats {
        display: flex;
        justify-content: space-around;
        margin: 15px 0;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
      }

      .stat {
        text-align: center;
      }

      .stat-number {
        font-size: 1.2em;
        font-weight: 600;
        color: #007bff;
      }

      .stat-label {
        font-size: 0.7em;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Enhanced Ad Blocker</h1>
        <div class="status-indicator">
          <div class="status-dot" id="statusDot"></div>
          <span class="status-text" id="blockingStatusText">Loading...</span>
        </div>
      </div>

      <div class="stats" id="statsSection">
        <div class="stat">
          <div class="stat-number" id="blockedCount">0</div>
          <div class="stat-label">Blocked Today</div>
        </div>
        <div class="stat">
          <div class="stat-number" id="totalBlocked">0</div>
          <div class="stat-label">Total Blocked</div>
        </div>
      </div>

      <div class="toggle-section">
        <span class="toggle-label">Global Protection</span>
        <label class="switch">
          <input type="checkbox" id="globalEnableSwitch" />
          <span class="slider"></span>
        </label>
      </div>

      <div id="siteSpecificSection" class="site-section" style="display: none">
        <h3>Current Site</h3>
        <div class="current-site" id="currentDomain">this site</div>
        <button id="whitelistButton" class="action-button primary">
          Whitelist Site
        </button>
      </div>
    </div>

    <div class="footer">
      <div class="footer-links">
        <a href="#" class="footer-link" id="optionsLink">Options</a>
        <a href="#" class="footer-link" id="helpLink">Help</a>
      </div>
      <div class="version">v0.3.0</div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
